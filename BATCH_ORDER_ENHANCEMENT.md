# 批量购买订单功能增强

## 概述
本次修改主要解决了数字资产订单系统中购买数量大于1时的库存扣减和用户资产创建问题。

## 问题分析
1. **购买数量验证缺失**：`addOrder`方法没有验证`buyQuantity`参数
2. **库存扣减逻辑错误**：目前只获取一个库存项，但购买数量可能大于1
3. **用户资产创建不完整**：`paySuccess`方法只创建一条用户资产记录，不管购买数量
4. **库存回退逻辑不完整**：超时回退时没有考虑购买数量

## 解决方案

### 1. 修改 `DigOrderCreateVo` 类
- 添加 `assetCodes` 字段（List<String>）存储多个资产编号
- 保留原有 `assetCode` 字段用于兼容性

### 2. 增强 `DigAssetInventoryService` 接口
- 新增 `getInventoryBatch(DigOrderCreateVo)` 方法：批量获取库存
- 新增 `returnInventoryBatch(DigOrderCreateVo)` 方法：批量回退库存

### 3. 修改 `addOrder` 方法
- 添加购买数量验证（必须大于0，不超过100）
- 使用批量库存获取方法
- 验证获取的库存数量是否满足购买需求
- 将多个资产编号存储到订单创建VO中

### 4. 增强 `paySuccess` 方法
- 支持处理批量资产编号
- 为每个资产编号创建对应的用户资产记录
- 批量更新库存状态为已售
- 支持盲盒的批量处理

### 5. 改进 `checkOverTimeOrder` 方法
- 支持批量库存回退
- 正确处理多个资产编号的超时回退

## 核心改进点

### 库存获取逻辑
```java
// 批量获取库存，使用分布式锁确保线程安全
List<DigAssetInventory> inventoryList = digAssetInventoryService.getInventoryBatch(digOrderCreateVo);
if (inventoryList.isEmpty() || inventoryList.size() != digOrderCreateVo.getBuyQuantity()) {
    return AjaxResult.error("库存不足");
}
```

### 用户资产创建逻辑
```java
// 为每个资产编号创建用户资产记录
for (String assetCode : assetCodes) {
    DigDataAsset dataAsset = new DigDataAsset();
    // ... 设置属性
    dataAsset.setAssetCode(assetCode);
    digDataAssetMapper.insert(dataAsset);
    sender.sendMsg(CacheConstants.CHAIN_QUEUE, dataAsset);
}
```

### 库存回退逻辑
```java
// 批量回退库存
if (!assetCodes.isEmpty()) {
    if (assetCodes.size() == 1) {
        digAssetInventoryService.returnInventory(digOrderCreateVo);
    } else {
        digAssetInventoryService.returnInventoryBatch(digOrderCreateVo);
    }
}
```

## 安全性考虑
1. **分布式锁**：使用Redisson分布式锁确保库存操作的原子性
2. **事务管理**：关键操作使用`@Transactional`注解确保数据一致性
3. **购买限制**：单次购买数量限制为100，防止恶意请求
4. **库存验证**：严格验证获取的库存数量与购买数量是否匹配

## 兼容性
- 保留原有的`assetCode`字段，确保单个购买的向后兼容
- 新增的批量处理逻辑对现有单个购买流程无影响
- 支持渐进式升级，可以逐步启用批量功能

## 测试建议
1. **单元测试**：验证批量库存获取和回退逻辑
2. **集成测试**：测试完整的批量购买流程
3. **并发测试**：验证高并发场景下的库存一致性
4. **边界测试**：测试库存不足、购买数量异常等边界情况

## 性能影响
- 批量操作可能增加数据库负载，建议监控数据库性能
- Redis操作频率增加，需要关注Redis内存使用
- 建议在生产环境中逐步放开批量购买功能

## 后续优化建议
1. 考虑添加订单明细表，更好地管理批量购买的资产信息
2. 优化批量操作的数据库查询性能
3. 添加更详细的日志记录和监控指标
4. 考虑实现库存预留机制，进一步优化用户体验
