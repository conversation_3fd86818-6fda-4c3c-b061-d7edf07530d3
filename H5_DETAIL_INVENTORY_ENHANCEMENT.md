# H5Detail接口库存信息增强

## 概述
本次修改为H5Detail接口添加了实时库存信息返回功能，提供了更丰富的资产详情数据，包括实时库存状态、销售状态等信息。

## 修改内容

### 1. 新增响应对象 `DigDigitalAssetResp`
创建了专门的响应对象，包含以下库存相关字段：

```java
// 库存相关信息
private Integer availableStock;        // 可购买库存数量
private Integer soldCount;             // 已售数量
private String stockStatus;            // 库存状态：AVAILABLE/LOW_STOCK/SOLD_OUT
private LocalDateTime stockUpdateTime; // 库存更新时间
private Boolean isSoldOut;             // 是否售罄
private Double stockPercentage;        // 库存百分比

// 销售相关信息
private Boolean canPurchase;           // 是否可购买
private String cannotPurchaseReason;   // 不可购买原因
private String saleStatus;             // 销售状态：NOT_STARTED/ON_SALE/ENDED
```

### 2. 新增库存信息对象 `DigAssetInventoryInfo`
创建了详细的库存信息对象，包含：
- 总发行数量、可购买库存、已售数量
- 锁定数量（待支付订单占用）
- 空投和活动库存信息
- 库存状态和百分比计算

### 3. 增强 `DigAssetInventoryService`
新增方法：
```java
DigAssetInventoryInfo getAssetInventoryInfo(Long assetId);
```

实现了完整的库存信息获取逻辑：
- 从Redis缓存获取实时库存数量
- 计算已售数量和库存百分比
- 统计待支付订单的锁定数量
- 判断库存状态（充足/不足/售罄）

### 4. 增强 `DigDigitalAssetService`
新增方法：
```java
DigDigitalAssetResp getAssetDetailResp(Long assetId);
```

实现了完整的资产详情响应构建：
- 复制基础资产信息
- 设置图片URL（通过AttachmentInfoService）
- 获取发行方、系列、专区信息
- 集成实时库存信息
- 判断销售状态和购买资格

### 5. 修改 H5Detail 接口
```java
@GetMapping("/client/detail/{assetId}")
public AjaxResult H5Detail(@PathVariable("assetId") Long assetId) {
    DigDigitalAssetResp assetResp = digDigitalAssetService.getAssetDetailResp(assetId);
    if (assetResp == null) {
        return error("资产不存在");
    }
    return success(assetResp);
}
```

**重要变更**：
- 移除了Redis缓存机制，确保返回实时库存数据
- 使用新的响应对象，提供更丰富的信息

## 核心功能

### 实时库存计算
- **可购买库存**：从Redis缓存中获取实时数量
- **已售数量**：总发行量 - 可购买库存
- **锁定数量**：统计状态为"待支付"的订单数量
- **库存百分比**：(剩余库存/总库存) × 100

### 库存状态判断
```java
if (availableStock <= 0) {
    stockStatus = "SOLD_OUT";
    isSoldOut = true;
} else if (availableStock <= totalQuantity * 0.1) {
    stockStatus = "LOW_STOCK";
    isSoldOut = false;
} else {
    stockStatus = "AVAILABLE";
    isSoldOut = false;
}
```

### 购买资格判断
```java
if (资产状态 == "已上架") {
    if (已售罄) {
        canPurchase = false;
        cannotPurchaseReason = "已售罄";
    } else {
        canPurchase = true;
    }
} else {
    canPurchase = false;
    cannotPurchaseReason = "资产未上架";
}
```

## API响应示例

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "assetId": 1,
    "assetName": "数字艺术品001",
    "assetCover": "https://example.com/cover.jpg",
    "issueQuantity": 1000,
    "issuePrice": 99.99,
    "availableStock": 750,
    "soldCount": 250,
    "stockStatus": "AVAILABLE",
    "stockPercentage": 75.0,
    "isSoldOut": false,
    "canPurchase": true,
    "cannotPurchaseReason": null,
    "saleStatus": "ON_SALE",
    "stockUpdateTime": "2025-07-30 14:30:00",
    "issuers": [...],
    "seriesName": "艺术系列A",
    "zoneName": "数字艺术专区"
  }
}
```

## 性能考虑

### 优化点
1. **实时性优先**：移除缓存确保库存数据的实时性
2. **Redis优化**：库存数据主要从Redis获取，减少数据库查询
3. **分布式锁**：库存操作使用分布式锁确保数据一致性

### 注意事项
1. **高并发场景**：库存查询可能增加Redis负载
2. **缓存策略**：可考虑对非库存信息（如图片URL、发行方信息）进行短时间缓存
3. **监控建议**：建议监控接口响应时间和Redis性能

## 兼容性
- 保持了原有接口路径不变
- 响应数据结构向后兼容，新增字段不影响现有客户端
- 可以根据需要逐步迁移到新的响应格式

## 后续优化建议
1. 考虑添加库存变化的WebSocket推送
2. 实现库存预警机制
3. 添加库存历史记录和统计分析
4. 优化高并发场景下的性能表现
